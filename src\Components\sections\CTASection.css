.cta-section {
  min-height: 100vh;
  background: #ffffff;
  padding: 80px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.cta-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.cta-container {
  max-width: 1200px;
  width: 100%;
  position: relative;
  z-index: 2;
}

/* Company Logos Carousel - Full Width Black Strip */
.companies-carousel-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  background: #000000;
  padding: 30px 0;
  overflow: hidden;
  z-index: 10;
}

.companies-carousel {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.carousel-track {
  display: flex;
  align-items: center;
  gap: 80px;
  animation: scrollLeft 16s linear infinite;
  width: calc(200% + 160px); /* <PERSON><PERSON> para 8 logos + gaps */
}

.company-logo-carousel {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.company-logo-carousel .logo-image {
  filter: brightness(0) invert(1);
  opacity: 0.8;
  transition: opacity 0.3s ease;
  max-height: 45px;
  width: auto;
}

.company-logo-carousel:hover .logo-image {
  opacity: 1;
}

/* Main CTA Content */
.cta-content {
  display: flex;
  justify-content: center;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.8s forwards;
}

.cta-card {
  background: linear-gradient(135deg, rgba(240, 240, 240, 0.8) 0%, rgba(250, 250, 250, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: 50px;
  padding: 50px 70px;
  display: flex;
  align-items: center;
  gap: 50px;
  max-width: 850px;
  width: 100%;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.cta-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50px;
  padding: 2px;
  background: linear-gradient(135deg, #C636FF 0%, #156CFF 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  z-index: -1;
}

.cta-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(198, 54, 255, 0.2);
}

/* CTA Text */
.cta-text {
  flex: 1;
  text-align: left;
}

.star-icon {
  margin-bottom: 20px;
  opacity: 0;
  animation: fadeIn 1s ease 1.2s forwards;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2a2a2a;
  margin-bottom: 15px;
  line-height: 1.2;
}

.cta-title .highlight {
  background: linear-gradient(90deg, #C636FF 0%, #156CFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-subtitle {
  font-size: 1.2rem;
  color: rgba(42, 42, 42, 0.7);
  margin: 0;
  font-weight: 400;
}

/* CTA Action */
.cta-action {
  display: flex;
  align-items: center;
}

.cta-button {
  background: linear-gradient(135deg, #B30FDC 0%, #156CFF 100%);
  color: white;
  border: none;
  padding: 20px 40px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  animation: slideInRight 0.8s ease 1.5s forwards;
  min-width: 180px;
  justify-content: center;
  height: 60px;
}

.cta-button:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 30px rgba(179, 15, 220, 0.4);
}

.cta-button:hover svg {
  transform: translateX(5px);
}

/* Background Decorations */
.background-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(198, 54, 255, 0.05) 0%, rgba(21, 108, 255, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.decoration-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scrollLeftInfinite {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-100% / 2));
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .cta-card {
    flex-direction: column;
    text-align: center;
    gap: 40px;
    padding: 40px 50px;
    border-radius: 40px;
  }

  .carousel-track {
    gap: 60px;
  }

  .company-logo-carousel {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .cta-section {
    padding: 60px 20px 120px 20px; /* Más padding bottom para el carousel */
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-card {
    padding: 35px 40px;
    border-radius: 35px;
  }

  .cta-button {
    padding: 18px 35px;
    font-size: 1rem;
    min-width: 160px;
    border-radius: 35px;
    height: 55px;
  }

  .carousel-track {
    gap: 50px;
  }

  .company-logo-carousel .logo-image {
    max-height: 40px;
  }

  .companies-carousel-container {
    padding: 25px 0;
  }
}

@media (max-width: 480px) {
  .cta-section {
    padding: 40px 15px 100px 15px;
  }

  .cta-card {
    padding: 30px 25px;
    border-radius: 30px;
  }

  .cta-title {
    font-size: 1.8rem;
  }

  .cta-button {
    padding: 16px 30px;
    font-size: 0.95rem;
    min-width: 140px;
    border-radius: 30px;
    height: 50px;
  }

  .carousel-track {
    gap: 40px;
  }

  .company-logo-carousel {
    min-width: 80px;
  }

  .company-logo-carousel .logo-image {
    max-height: 35px;
  }

  .companies-carousel-container {
    padding: 20px 0;
  }
}
