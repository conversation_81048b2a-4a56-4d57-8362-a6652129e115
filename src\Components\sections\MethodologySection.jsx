import { useState, useEffect } from 'react';
import ArrowIcon from '../../assets/SVG/ArrowIcon';
import './MethodologySection.css';

/**
 * Sección de Metodología
 * Muestra los tres frameworks: IT Blackbook, Security Blackbook y Greta Playbook
 */
const MethodologySection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const frameworks = [
    {
      title: "IT Blackbook",
      color: "dark",
      features: [
        "Proceso principal (venta completa)",
        "Mapeo de macro procesos",
        "Mapeo con sistemas",
        "Roadmap tecnológico + protección de datos",
        "Procesos de tecnología basado en APQC"
      ]
    },
    {
      title: "Security Blackbook", 
      color: "green",
      features: [
        "Security Vision",
        "Taller de identificación de activos",
        "Diseño se estrategia de mitigación",
        "Primera evaluación de seguridad",
        "Quick wins (1 mes max)",
        "Security Roadmap Security plan and check"
      ]
    },
    {
      title: "Greta Playbook",
      color: "light",
      features: [
        "Metodologías (Scrum, JTBD, PVM)",
        "Gestión de proyectos",
        "Diseño de productos digitales",
        "Agilidad empresarial",
        "Transformación digital"
      ]
    }
  ];

  return (
    <section className={`methodology-section ${isVisible ? 'visible' : ''}`}>
      <div className="methodology-container">
        {/* Header */}
        <div className="methodology-header">
          <h2 className="methodology-title">Nuestra Metodología</h2>
          <div className="title-underline"></div>
          <p className="methodology-description">
            Desarrollamos nuestros propios Frameworks en nuestro laboratorio para nuestros clientes 
            buscando la implementación de soluciones y servicios para la mejora de sus procesos.
          </p>
        </div>

        {/* Frameworks Grid */}
        <div className="frameworks-grid">
          {frameworks.map((framework, index) => (
            <div 
              key={framework.title}
              className={`framework-card ${framework.color}`}
              style={{ '--delay': `${index * 0.3}s` }}
            >
              <div className="framework-header">
                <h3 className="framework-title">{framework.title}</h3>
                <div className="framework-arrow">
                  <ArrowIcon width={24} height={24} direction="up" />
                </div>
              </div>

              <div className="framework-content">
                <ul className="framework-features">
                  {framework.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="framework-feature">
                      • {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MethodologySection;
