.methodology-section {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
  padding: 80px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.methodology-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.methodology-container {
  max-width: 1400px;
  width: 100%;
}

/* Header */
.methodology-header {
  text-align: center;
  margin-bottom: 80px;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.3s forwards;
}

.methodology-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.title-underline {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #00E5FF 0%, #ADFF4D 100%);
  margin: 0 auto 40px;
  border-radius: 2px;
}

.methodology-description {
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.2rem;
  line-height: 1.6;
  color: #555;
}

/* Frameworks Grid */
.frameworks-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.framework-card {
  border-radius: 30px;
  padding: 40px 30px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.framework-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Framework Card Variants */
.framework-card.dark {
  background: #2a2a2a;
  color: white;
}

.framework-card.green {
  background: linear-gradient(135deg, #ADFF4D 0%, #00E5FF 100%);
  color: #333;
}

.framework-card.light {
  background: #ffffff;
  color: #333;
  border: 2px solid #e0e0e0;
}

/* Framework Header */
.framework-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.framework-title {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1.2;
  flex: 1;
}

.framework-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  transition: all 0.3s ease;
}

.framework-card.dark .framework-arrow {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.framework-card.green .framework-arrow {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.framework-card.light .framework-arrow {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.framework-card:hover .framework-arrow {
  transform: rotate(45deg);
}

/* Framework Content */
.framework-content {
  flex: 1;
}

.framework-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.framework-feature {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 12px;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.framework-card:hover .framework-feature {
  opacity: 1;
}

/* Decorative Elements */
.framework-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.framework-card:hover::before {
  opacity: 1;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .frameworks-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .framework-card {
    min-height: auto;
  }
}

@media (max-width: 768px) {
  .methodology-section {
    padding: 60px 20px;
  }

  .methodology-title {
    font-size: 2.5rem;
  }

  .methodology-description {
    font-size: 1.1rem;
  }

  .framework-card {
    padding: 30px 20px;
  }

  .framework-title {
    font-size: 1.5rem;
  }
}
